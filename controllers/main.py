from odoo import http
from odoo.http import request

class AdumoController(http.Controller):

    @http.route("/payment/adumo/return", type="http", auth="public", csrf=False)
    def adumo_return(self, **post):
        request.env["payment.transaction"].sudo()._handle_feedback_data("adumo", post)
        return request.redirect("/payment/status")

    @http.route("/payment/adumo/webhook", type="json", auth="public", methods=["POST"], csrf=False)
    def adumo_webhook(self, **payload):
        request.env["payment.transaction"].sudo()._handle_feedback_data("adumo", payload)
        return "OK"
