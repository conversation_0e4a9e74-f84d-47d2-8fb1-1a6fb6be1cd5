import logging
from odoo import http
from odoo.http import request

_logger = logging.getLogger(__name__)

class AdumoController(http.Controller):

    @http.route("/payment/adumo/return", type="json", auth="public", methods=["POST"], csrf=False)
    def adumo_return(self, **post):
        """ Handle return from Adumo payment page with JSON payload """
        _logger.info("Adumo return data received: %s", post)
        
        try:
            tx_sudo = request.env['payment.transaction'].sudo()._get_tx_from_notification_data('adumo', post)
            tx_sudo._handle_notification_data('adumo', post)
            
            return {
                'status': 'success',
                'redirect_url': '/payment/status'
            }
        except Exception as e:
            _logger.exception("Error processing Adumo return data: %s", e)
            return {
                'status': 'error',
                'message': str(e)
            }

    @http.route("/payment/adumo/webhook", type="json", auth="public", methods=["POST"], csrf=False)
    def adumo_webhook(self, **payload):
        """ Handle webhook notifications from Adumo """
        _logger.info("Adumo webhook data received: %s", payload)
        
        try:
            tx_sudo = request.env['payment.transaction'].sudo()._get_tx_from_notification_data('adumo', payload)
            tx_sudo._handle_notification_data('adumo', payload)
            return {"status": "OK"}
        except Exception as e:
            _logger.exception("Error processing Adumo webhook data: %s", e)
            return {"status": "ERROR", "message": str(e)}
