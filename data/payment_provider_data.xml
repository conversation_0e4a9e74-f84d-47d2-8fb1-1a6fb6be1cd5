<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <record id="payment_provider_adumo" model="payment.provider">
            <field name="name">Adumo Online</field>
            <field name="code">adumo</field>
            <field name="sequence">1</field>
            <field name="state">disabled</field>
            <field name="is_published">False</field>
            <!-- Link to Odoo's existing card payment method -->
            <field name="payment_method_ids" eval="[(6, 0, [ref('payment.payment_method_card')])]"/>
            <field name="image_128" type="base64" file="payment_adumo/static/description/icon.jpg"/>
        </record>

    </data>
</odoo>