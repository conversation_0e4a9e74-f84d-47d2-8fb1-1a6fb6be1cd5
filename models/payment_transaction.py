import logging
import jwt
import time
import uuid
from odoo import _, models
from odoo.exceptions import ValidationError

_logger = logging.getLogger(__name__)


class PaymentTransaction(models.Model):
    _inherit = 'payment.transaction'

    def _get_specific_processing_values(self, processing_values):
        """ Override of payment to return Adumo-specific processing values. """
        res = super()._get_specific_processing_values(processing_values)
        if self.provider_code != 'adumo':
            return res

        # Build the payload for Adumo
        payload = self._adumo_build_payload()

        return {
            'api_url': self.provider_id._adumo_get_endpoint(),
            'payload': payload,
        }

    def _get_specific_rendering_values(self, processing_values):
        """ Override of payment to return Adumo-specific rendering values. """
        res = super()._get_specific_rendering_values(processing_values)
        if self.provider_code != 'adumo':
            return res

        res.update({
            'form_action': processing_values.get('api_url'),
            'hidden_inputs': processing_values.get('payload', {}),
        })
        
        return res

    def _adumo_build_payload(self):
        """ Build complete Adumo payload with all required fields. """
        self.ensure_one()
        now = int(time.time())
        
        # Format amount as decimal string
        amount_decimal = f"{self.amount:.2f}"
        
        # Build the JWT payload
        jwt_payload = {
            "cuid": self.provider_id.adumo_merchant_uid,
            "auid": self.provider_id.adumo_application_uid,
            "mref": self.reference,
            "amount": amount_decimal,
            "iat": now,
            "exp": now + 7200,  # 2 hours expiry
        }
        
        # Generate JWT token
        token = jwt.encode(jwt_payload, self.provider_id.adumo_jwt_secret, algorithm="HS256")
        
        # Build redirect URLs
        base_url = self.provider_id.get_base_url().rstrip('/')
        return_url = f"{base_url}/payment/adumo/return"
        
        # Generate unique PUID for this transaction
        puid = str(uuid.uuid4())
        
        # Return complete payload matching Adumo's expected format
        payload = {
            'puid': puid,
            'MerchantID': self.provider_id.adumo_merchant_uid,
            'ApplicationID': self.provider_id.adumo_application_uid,
            'MerchantReference': self.reference,
            'Amount': amount_decimal,
            'Token': token,
            'txtCurrencyCode': self.currency_id.name,
            'RedirectSuccessfulURL': return_url,
            'RedirectFailedURL': return_url,
        }
        
        # Add optional customer information if available
        if self.partner_name:
            payload['Recipient'] = self.partner_name
        if self.partner_address:
            payload['ShippingAddress1'] = self.partner_address
        if self.partner_city:
            payload['ShippingAddress2'] = self.partner_city
        if self.partner_state_id:
            payload['ShippingAddress3'] = self.partner_state_id.name
        if self.partner_zip:
            payload['ShippingAddress4'] = self.partner_zip
        if self.partner_country_id:
            payload['ShippingAddress5'] = self.partner_country_id.name
            
        return payload

    def _get_tx_from_notification_data(self, provider_code, notification_data):
        """ Override of payment to find the transaction based on Adumo data.
        
        :param str provider_code: The code of the provider that handled the transaction
        :param dict notification_data: The notification data sent by the provider
        :return: The transaction if found
        :rtype: recordset of `payment.transaction`
        :raise: ValidationError if the data match no transaction
        """
        tx = super()._get_tx_from_notification_data(provider_code, notification_data)
        if provider_code != 'adumo' or len(tx) == 1:
            return tx

        # Extract merchant reference to find the transaction (from your controller logic)
        merchant_ref = notification_data.get('_MERCHANTREFERENCE')
        
        if not merchant_ref:
            raise ValidationError("Adumo: No merchant reference found in notification data")
            
        # Find the transaction
        tx = self.search([
            ('reference', '=', merchant_ref),
            ('provider_code', '=', 'adumo')
        ])
        
        if not tx:
            raise ValidationError(f"Adumo: No transaction found for reference {merchant_ref}")
            
        if len(tx) > 1:
            _logger.warning("Multiple transactions found for reference: %s", merchant_ref)
            tx = tx[0]  # Take the first one
        
        return tx

    def _process_notification_data(self, notification_data):
        """ Override of payment to process the transaction based on Adumo data.
        
        Note: self.ensure_one()
        
        :param dict notification_data: The notification data sent by the provider
        :return: None
        """
        super()._process_notification_data(notification_data)
        if self.provider_code != 'adumo':
            return

        # Use the complete logic from your controller
        try:
            # Validate JWT token if present
            jwt_token = notification_data.get('_RESPONSE_TOKEN')
            if jwt_token:
                try:
                    decoded_jwt = jwt.decode(
                        jwt_token,
                        self.provider_id.adumo_jwt_secret,
                        algorithms=["HS256"],
                        leeway=10
                    )
                    _logger.info("JWT decoded successfully: %s", decoded_jwt)
                    
                    # Validate JWT claims match transaction
                    expected_amount = f"{self.amount:.2f}"
                    if (
                        decoded_jwt.get("amount") != expected_amount or
                        decoded_jwt.get("cuid") != self.provider_id.adumo_merchant_uid or
                        decoded_jwt.get("auid") != self.provider_id.adumo_application_uid or
                        decoded_jwt.get("mref") != self.reference
                    ):
                        self._set_error("JWT claims validation failed")
                        return
                        
                except jwt.InvalidTokenError as e:
                    _logger.warning("Invalid JWT token: %s", e)
                    self._set_error(f"Invalid JWT token: {e}")
                    return
            
            # Update provider reference
            transaction_index = notification_data.get('_TRANSACTIONINDEX')
            if transaction_index:
                self.provider_reference = transaction_index
            
            # Process transaction status (using Adumo's actual response format)
            result_code = notification_data.get('_RESULT', '').strip()
            status = notification_data.get('_STATUS', '').upper()
            
            # Map Adumo status to Odoo transaction states
            if result_code == '0' and status == 'AUTHORISED':
                # Successful payment
                self._set_done()
                _logger.info("Transaction %s marked as done", self.reference)
            elif status in ['PENDING', 'PROCESSING']:
                # Payment is pending
                self._set_pending()
                _logger.info("Transaction %s marked as pending", self.reference)
            else:
                # Failed payment
                error_msg = notification_data.get('_BANK_ERROR_MESSAGE', 'Payment failed')
                self._set_canceled(state_message=error_msg)
                _logger.info("Transaction %s marked as canceled: %s", self.reference, error_msg)
                
        except Exception as e:
            _logger.exception("Error processing Adumo data for transaction %s: %s", self.reference, e)
            self._set_error(f"Processing error: {e}")