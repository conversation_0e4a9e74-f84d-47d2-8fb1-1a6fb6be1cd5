from  odoo import models, fields, api, _
from odoo.exceptions import ValidationError

import jwt, time 


class PaymentProviderAdumo(models.Model):
    # This model extends the payment.provider model to add Adumo Online as a payment provider.
    _inherit = 'payment.provider'

    # Defining the provider name and other attributes.
    code=fields.Selection(
        selection_add=[('adumo', 'Adumo Online')],
        ondelete={'adumo': 'set default'}
    )

    # ▶ Adumo credentials ----------------------------------------------------
    adumo_merchant_uid    = fields.Char("Merchant UID (cuid)", required_if_provider="adumo")
    adumo_application_uid = fields.Char("Application UID (auid)", required_if_provider="adumo")
    adumo_jwt_secret      = fields.Char("JWT Secret", required_if_provider="adumo")

    # Overriding the _compute_feature_support_fields method from payment.provider model to
    # ensure values are set for Adumo providers
    @api.depends('code')
    def _compute_feature_support_fields(self):
        """ Override to set Adumo-specific feature support. """
        super()._compute_feature_support_fields()
        adumo_providers = self.filtered(lambda p: p.code == 'adumo')
        adumo_providers.update({
            'support_tokenization': True,
            'support_manual_capture': 'full_only',
            'support_refund': 'partial',
        
        })


    # -----------------------------------------------------------------------
    def _check_credentials(self):
        super()._check_credentials()
        for prov in self.filtered(lambda p: p.code == "adumo"):
            if not (prov.adumo_jwt_secret and prov.adumo_merchant_uid and prov.adumo_application_uid):
                raise ValidationError(_("All Adumo credential fields are mandatory."))

    # Helpers ----------------------------------------------------------------
    def _adumo_endpoint(self):
        self.ensure_one()
        return (
            "https://staging-apiv3.adumoonline.com/product/payment/v1/initialisevirtual"
            if self.state == "test"
            else "https://apiv3.adumoonline.com/product/payment/v1/initialisevirtual"
        )

    def _adumo_build_payload(self, values):
        """Assemble the JWT payload, then sign it (HS256)."""
        self.ensure_one()
        now = int(time.time())
        payload = {
            "cuid": self.adumo_merchant_uid,
            "auid": self.adumo_application_uid,
            "mref": values["reference"],
            "amount": int(values["amount"] * 100),   # cents
            "iat": now,
            "exp": now + 7200,                       # 2h expiry
            "notificationURL": values["callback_url"],
        }
        token = jwt.encode(payload, self.adumo_jwt_secret, algorithm="HS256")
        return {"Token": token}

    # Main hook --------------------------------------------------------------
    def _send_payment_request(self, values):
        if self.code != "adumo":
            return super()._send_payment_request(values)

        endpoint = self._adumo_endpoint()
        data = self._adumo_build_payload(values)
        data.update({
            "RedirectSuccessfulURL": values["return_url"],
            "RedirectFailedURL": values["return_url"],
            "Mode": 1,  # per odoo docs, always 1
        })
        return {
            "type": "form",
            "method": "post",
            "url": endpoint,
            "data": data,
        }

    # Webhook / return -------------------------------------------------------
    def _process_feedback_data(self, data):
        if self.code != "adumo":
            return super()._process_feedback_data(data)

        tx = self.env["payment.transaction"]._get_tx_from_reference(data.get("mref"))
        token = data.get("_RESPONSE_TOKEN")
        if not token:
            tx._set_error(_("Missing JWT in Adumo response"))
            return

        try:
            decoded = jwt.decode(
                token, self.adumo_jwt_secret, algorithms=["HS256"], leeway=10
            )
        except Exception as e:
            tx._set_error(_("Invalid JWT: %s") % e)
            return

        # validate core fields
        if (
            decoded.get("amount") != int(tx.amount * 100)
            or decoded.get("cuid") != self.adumo_merchant_uid
            or decoded.get("auid") != self.adumo_application_uid
        ):
            tx._set_error(_("JWT claims do not match transaction"))
            return

        status = decoded.get("result")
        if status == "000":          # success result per odoo docs
            tx._set_done()
        elif status == "001":        # pending example
            tx._set_pending()
        else:
            tx._set_canceled(reason=decoded.get("resultDescription"))