import time
import jwt
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class PaymentProviderAdumo(models.Model):
    # This model extends the payment.provider model to add Adumo Online as a payment provider.
    _inherit = 'payment.provider'

    # Defining the provider name and other attributes.
    code = fields.Selection(
        selection_add=[('adumo', 'Adumo Online')],
        ondelete={'adumo': 'set default'}
    )

    # ▶ Adumo credentials ----------------------------------------------------
    adumo_merchant_uid = fields.Char("Merchant UID (cuid)", required_if_provider="adumo")
    adumo_application_uid = fields.Char("Application UID (auid)", required_if_provider="adumo")
    adumo_jwt_secret = fields.Char("JWT Secret", required_if_provider="adumo", groups='base.group_system')

    @api.depends('code')
    def _compute_feature_support_fields(self):
        """ Override to set Adumo-specific feature support. """
        super()._compute_feature_support_fields()
        adumo_providers = self.filtered(lambda p: p.code == 'adumo')
        adumo_providers.update({
            'support_tokenization': False,  
            'support_manual_capture': False,
            'support_refund': 'partial',
        })

    def _get_default_payment_method_codes(self):
        """ Return the default payment methods for this provider. """
        self.ensure_one()
        if self.code != 'adumo':
            return super()._get_default_payment_method_codes()
        return {'card'}

    # Helpers ----------------------------------------------------------------
    def _adumo_get_endpoint(self):
        """ Get the appropriate Adumo endpoint based on provider state. """
        self.ensure_one()
        if self.state == "test":
            return "https://staging-apiv3.adumoonline.com/product/payment/v1/initialisevirtual"
        else:
            return "https://apiv3.adumoonline.com/product/payment/v1/initialisevirtual"