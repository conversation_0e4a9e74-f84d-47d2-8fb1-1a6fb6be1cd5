# Adumo Online – Payment Gateway

An Odoo module that integrates Adumo Online payment gateway for secure card payments through hosted payment pages.

## Overview

This module extends Odoo's payment system to support Adumo Online as a payment provider, enabling businesses to accept credit and debit card payments securely through Adumo's hosted payment pages.

## Features

- **Secure Payment Processing**: Utilizes Adumo Online's hosted payment pages for PCI-compliant transactions
- **JWT-based Authentication**: Secure communication using JSON Web Tokens with HS256 algorithm
- **Multiple Payment States**: Supports success, pending, and failed payment states
- **Webhook Support**: Real-time payment status updates via webhooks
- **Test & Production Modes**: Separate endpoints for testing and live transactions
- **Tokenization Support**: Enables saving payment methods for future use
- **Partial Refunds**: Support for partial transaction refunds
- **Manual Capture**: Full manual capture support for authorized transactions

## Requirements

- Odoo 18.0+
- Python packages:
  - `PyJWT` for JWT token handling
- Adumo Online merchant account with:
  - Merchant UID (cuid)
  - Application UID (auid)
  - JWT Secret

## Installation

1. Clone or download this module to your Odoo addons directory:
   ```bash
   git clone <repository-url> /path/to/odoo/addons/payment_adumo
   ```

2. Install required Python dependencies:
   ```bash
   pip install PyJWT
   ```

3. Update your Odoo addons list and install the module:
   - Go to Apps menu in Odoo
   - Click "Update Apps List"
   - Search for "Adumo Online"
   - Click Install

## Configuration

### 1. Enable the Payment Provider

1. Navigate to **Accounting > Configuration > Payment Providers**
2. Find "Adumo Online" in the list
3. Click to open the configuration

### 2. Configure Credentials

Fill in the required Adumo credentials:

- **Merchant UID (cuid)**: Your Adumo merchant identifier
- **Application UID (auid)**: Your Adumo application identifier  
- **JWT Secret**: Your JWT signing secret from Adumo

### 3. Set Environment

- **Test Mode**: Uses Adumo staging environment (`staging-apiv3.adumoonline.com`)
- **Production Mode**: Uses Adumo live environment (`apiv3.adumoonline.com`)

### 4. Enable the Provider

1. Set the state to "Enabled"
2. Check "Published" to make it available on your website
3. Save the configuration

## Usage

Once configured, Adumo Online will appear as a payment option during checkout:

1. Customers select "Adumo Online" as their payment method
2. They are redirected to Adumo's secure hosted payment page
3. After payment, customers are redirected back to your Odoo site
4. Payment status is updated via webhook notifications

## Technical Details

### API Endpoints

The module uses Adumo's Payment API v1:
- **Test**: `https://staging-apiv3.adumoonline.com/product/payment/v1/initialisevirtual`
- **Production**: `https://apiv3.adumoonline.com/product/payment/v1/initialisevirtual`

### Webhook Endpoints

The module provides these endpoints for Adumo callbacks:
- **Return URL**: `/payment/adumo/return` - Handles customer redirects
- **Webhook URL**: `/payment/adumo/webhook` - Handles server-to-server notifications

### JWT Payload Structure

```json
{
  "cuid": "merchant_uid",
  "auid": "application_uid", 
  "mref": "transaction_reference",
  "amount": 1000,
  "iat": **********,
  "exp": **********,
  "notificationURL": "webhook_url"
}
```

### Payment Result Codes

- `000`: Success - Payment completed successfully
- `001`: Pending - Payment is being processed
- Other codes: Failed - Payment was declined or failed

## File Structure

```
payment_adumo/
├── __init__.py
├── __manifest__.py
├── controllers/
│   ├── __init__.py
│   └── main.py              # Webhook and return URL handlers
├── data/
│   └── payment_provider_data.xml  # Default provider configuration
├── models/
│   ├── __init__.py
│   └── payment_provider.py  # Main payment provider logic
├── security/
│   └── ir.model.access.csv  # Access rights
├── static/
│   ├── description/
│   │   └── icon.jpg         # Module icon
│   └── src/
│       └── js/
│           └── payment_adumo.js  # Frontend JavaScript
└── views/
    ├── payment_provider_views.xml  # Backend configuration views
    └── templates.xml        # Frontend payment templates
```

## Security Considerations

- All sensitive credentials are stored securely in Odoo's encrypted fields
- JWT tokens have a 2-hour expiration time
- All communication with Adumo uses HTTPS
- Webhook data is validated against JWT signatures
- Transaction amounts and merchant details are verified on callback

## Troubleshooting

### Common Issues

1. **"Missing JWT in Adumo response"**
   - Check webhook URL configuration in Adumo dashboard
   - Verify webhook endpoint is accessible

2. **"Invalid JWT" errors**
   - Verify JWT Secret is correct
   - Check system time synchronization (JWT uses timestamps)

3. **"JWT claims do not match transaction"**
   - Ensure Merchant UID and Application UID are correct
   - Verify transaction amounts match

### Debug Mode

Enable Odoo's debug mode to see detailed error messages and logs.

## Support

For technical support:
- Check Odoo logs for detailed error messages
- Verify Adumo dashboard for transaction status
- Contact AO Group for module-specific issues
- Contact Adumo support for payment gateway issues

## License

This module is licensed under LGPL-3.

## Version

Current version: 18.0.1.0.0

Compatible with Odoo 18.0 and later versions.
